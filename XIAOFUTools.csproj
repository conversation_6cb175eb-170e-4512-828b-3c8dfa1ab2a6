<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>

    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <NoWarn>CA1416</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Tools\新文件夹\**" />
    <EmbeddedResource Remove="Tools\新文件夹\**" />
    <None Remove="Tools\新文件夹\**" />
    <Page Remove="Tools\新文件夹\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Config.daml" />

    <None Remove="Images\AreaCalculator_16.png" />
    <None Remove="Images\AreaCalculator_32.png" />
    <None Remove="Images\AreaSplit_16.png" />
    <None Remove="Images\AreaSplit_32.png" />
    <None Remove="Images\BatchAddData_16.png" />
    <None Remove="Images\BatchAddData_32.png" />
    <None Remove="Images\BatchLayerClip_16.png" />
    <None Remove="Images\BatchLayerClip_32.png" />
    <None Remove="Images\BatchProjectionDefinition_16.png" />
    <None Remove="Images\BatchProjectionDefinition_32.png" />
    <None Remove="Images\BatchGeometryRepair_16.png" />
    <None Remove="Images\BatchGeometryRepair_32.png" />
    <None Remove="Images\BoundaryPointGenerator_16.png" />
    <None Remove="Images\BoundaryPointGenerator_32.png" />
    <None Remove="Images\ChineseNumbering_16.png" />
    <None Remove="Images\ChineseNumbering_32.png" />
    <None Remove="Images\FieldCopyTool_16.png" />
    <None Remove="Images\FieldCopyTool_32.png" />
    <None Remove="Images\Globe16.png" />
    <None Remove="Images\GroupNumbering_16.png" />
    <None Remove="Images\GroupNumbering_32.png" />
    <None Remove="Images\PresetLayers_16.png" />
    <None Remove="Images\PresetLayers_32.png" />
    <None Remove="Images\RangeClipTool_16.png" />
    <None Remove="Images\RangeClipTool_32.png" />
    <None Remove="Images\SpecialCoordinateTransform_16.png" />
    <None Remove="Images\SpecialCoordinateTransform_32.png" />
    <None Remove="Images\Toolbox_16.png" />
    <None Remove="Images\Toolbox_32.png" />
    <None Remove="Images\TxtToFeature_16.png" />
    <None Remove="Images\TxtToFeature_32.png" />
    <None Remove="Images\ViewArea_16.png" />
    <None Remove="Images\ViewArea_32.png" />
    <None Remove="Images\OCRCoordinateTable_16.png" />
    <None Remove="Images\OCRCoordinateTable_32.png" />
    <None Remove="Images\AIAssistant_16.png" />
    <None Remove="Images\AIAssistant_32.png" />
    <None Remove="Images\FeatureToTxt_16.png" />
    <None Remove="Images\FeatureToTxt_32.png" />
    <None Remove="Images\DownloadOnlineImagery_16.png" />
    <None Remove="Images\DownloadOnlineImagery_32.png" />
    <None Remove="Images\ExportLayout_16.png" />
    <None Remove="Images\ExportLayout_32.png" />
    <None Remove="Images\Overture16.png" />
    <None Remove="Images\Overture32.png" />
    <None Remove="Images\OvertureLoader_16.png" />
    <None Remove="Images\OvertureLoader_32.png" />
    <None Remove="Images\About_16.png" />
    <None Remove="Images\About_32.png" />
    <None Remove="Images\OverlapCheck_16.png" />
    <None Remove="Images\OverlapCheck_32.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Config.daml" />
    <Content Include="Images\AddInDesktop16.png" />
    <Content Include="Images\AddInDesktop32.png" />
    <Content Include="Images\AreaCalculator_16.png" />
    <Content Include="Images\AreaCalculator_32.png" />
    <Content Include="Images\AreaSplit_16.png" />
    <Content Include="Images\AreaSplit_32.png" />
    <Content Include="Images\BatchAddData_16.png" />
    <Content Include="Images\BatchAddData_32.png" />
    <Content Include="Images\BatchLayerClip_16.png" />
    <Content Include="Images\BatchLayerClip_32.png" />
    <Content Include="Images\BatchProjectionDefinition_16.png" />
    <Content Include="Images\BatchProjectionDefinition_32.png" />
    <Content Include="Images\BatchGeometryRepair_16.png" />
    <Content Include="Images\BatchGeometryRepair_32.png" />
    <Content Include="Images\BoundaryPointGenerator_16.png" />
    <Content Include="Images\BoundaryPointGenerator_32.png" />
    <Content Include="Images\ChineseNumbering_16.png" />
    <Content Include="Images\ChineseNumbering_32.png" />
    <Content Include="Images\FieldCopyTool_16.png" />
    <Content Include="Images\FieldCopyTool_32.png" />
    <Content Include="Images\Globe16.png" />
    <Content Include="Images\GroupNumbering_16.png" />
    <Content Include="Images\GroupNumbering_32.png" />
    <Content Include="Images\PresetLayers_16.png" />
    <Content Include="Images\PresetLayers_32.png" />
    <Content Include="Images\RangeClipTool_16.png" />
    <Content Include="Images\RangeClipTool_32.png" />
    <Content Include="Images\SpecialCoordinateTransform_16.png" />
    <Content Include="Images\SpecialCoordinateTransform_32.png" />
    <Content Include="Images\Toolbox_16.png" />
    <Content Include="Images\Toolbox_32.png" />
    <Content Include="Images\TxtToFeature_16.png" />
    <Content Include="Images\TxtToFeature_32.png" />
    <Content Include="Images\ViewArea_16.png" />
    <Content Include="Images\ViewArea_32.png" />
    <Content Include="Images\AIAssistant_16.png" />
    <Content Include="Images\AIAssistant_32.png" />
    <Content Include="Images\FeatureToTxt_16.png" />
    <Content Include="Images\FeatureToTxt_32.png" />
    <Content Include="Images\DownloadOnlineImagery_16.png" />
    <Content Include="Images\DownloadOnlineImagery_32.png" />
    <Content Include="Images\ExportLayout_16.png" />
    <Content Include="Images\ExportLayout_32.png" />
    <Content Include="Images\Overture16.png" />
    <Content Include="Images\Overture32.png" />
    <Content Include="Images\OvertureLoader_16.png" />
    <Content Include="Images\OvertureLoader_32.png" />

    <!-- 工具图标文件 - 明亮主题 -->

    <!-- 工具图标文件 - 暗色主题 -->
    <Content Include="Images\Settings_16.png" />
    <Content Include="Images\Settings_32.png" />
    <Content Include="Images\Authorization_16.png" />
    <Content Include="Images\Authorization_32.png" />
    <Content Include="Images\About_16.png" />
    <Content Include="Images\About_32.png" />
    <Content Include="Images\NodeDistanceChecker_16.png" />
    <Content Include="Images\NodeDistanceChecker_32.png" />
    <Content Include="Images\OverlapCheck_16.png" />
    <Content Include="Images\OverlapCheck_32.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Tools\PresetLayers\PresetLayersGalleryTemplate.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Tools\AIAssistant\WebUI\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <EsriAddInX>true</EsriAddInX>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\launchSettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="ArcGIS.Desktop.Framework">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Desktop.Framework.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Core">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Core.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Core">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\Core\ArcGIS.Desktop.Core.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Mapping">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\Mapping\ArcGIS.Desktop.Mapping.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Catalog">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\Catalog\ArcGIS.Desktop.Catalog.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Editing">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\Editing\ArcGIS.Desktop.Editing.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Extensions">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\DesktopExtensions\ArcGIS.Desktop.Extensions.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.GeoProcessing">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\GeoProcessing\ArcGIS.Desktop.GeoProcessing.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Layouts">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\Layout\ArcGIS.Desktop.Layouts.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
	<Reference Include="ArcGIS.Desktop.KnowledgeGraph">
		<HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\Extensions\KnowledgeGraph\ArcGIS.Desktop.KnowledgeGraph.dll</HintPath>
		<CopyLocal>False</CopyLocal>
		<Private>False</Private>
	</Reference>
	<Reference Include="ArcGIS.Desktop.Shared.Wpf">
        <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Desktop.Shared.Wpf.dll</HintPath>
        <CopyLocal>False</CopyLocal>
        <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Ribbon.Wpf">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Desktop.Ribbon.Wpf.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.DataGrid.Contrib.Wpf">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Desktop.DataGrid.Contrib.Wpf.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArcGIS.Desktop.Resources">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ArcGIS.Desktop.Resources.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
    <Reference Include="ESRI.ArcGIS.ItemIndex">
      <HintPath>D:\RUANJIAN\ArcGIS\Pro\bin\ESRI.ArcGIS.ItemIndex.dll</HintPath>
      <CopyLocal>False</CopyLocal>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Update="Data\影像图层\Google01.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\Google02.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\Google带注记01-CGJ02.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\Google带注记02-CGJ02.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\Mapbox.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\Mapbox卫星混合图英文.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\openstreetmap.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\World_Imagery（世界影像）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\全球10m等高线地物地形图源a.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\全球10m等高线地物地形图源b.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-地形地图（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-地形注记（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-影像地图（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-影像注记（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-矢量地图（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\天地图-矢量注记（球面墨卡托投影）.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\慎用国外图源！涉及国界请用天地图图层.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星图-地形地图%28注记%29.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星图-地形地图.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星图-影像地图%28注记%29.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星图-影像地图.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星图-矢量地图.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星球地球影像底图.lyr">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\星球地球电子底图.lyr">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\混合参考图层.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\影像图层\高德-CGJ02.lyrx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="DuckDB.NET.Data.Full" Version="1.2.0" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3296.44" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
  </ItemGroup>
   <Import Project="D:\RUANJIAN\ArcGIS\Pro\bin\Esri.ProApp.SDK.Desktop.targets" Condition="Exists('D:\RUANJIAN\ArcGIS\Pro\bin\Esri.ProApp.SDK.Desktop.targets') AND !Exists('Esri.ArcGISPro.Extensions.targets')" />
</Project>
