# XIAOFU工具箱结构文档

## 项目概述

XIAOFUTools 是一个基于 ArcGIS Pro 的 C# 扩展工具箱，采用 .NET 8.0 框架开发，提供了丰富的 GIS 数据处理和分析功能。

### 技术架构
- **开发框架**: .NET 8.0-windows
- **UI框架**: WPF + WinForms 混合应用
- **架构模式**: MVVM (Model-View-ViewModel)
- **插件系统**: 基于 ArcGIS Pro Add-in 框架
- **主要依赖**: ArcGIS Pro SDK, DuckDB.NET, WebView2, Newtonsoft.Json

### 核心特性
- 模块化设计，每个工具都是独立的模块
- 完整的授权系统和权限管理
- 支持多种 GIS 数据格式处理
- 集成 AI 助手功能
- 现代化的 Web UI 界面
- 完善的错误处理和日志系统

## 整体架构层次

```
XIAOFU工具箱 (选项卡)
├── 通用工具 (大组)
│   ├── 通用 (小组/按钮面板)
│   │   ├── 批量添加数据
│   │   ├── 要素顺序编号
│   │   └── 地块中文编号
│   ├── 通用2 (小组/按钮面板)
│   │   ├── 按字段批量裁剪要素
│   │   ├── 字段复制工具
│   │   ├── 根据范围批量裁剪要素图层
│   │   ├── 生成四至坐标点
│   │   ├── 批量定义投影
│   │   └── 批量修复几何
│   ├── 查看面积 (独立按钮)
│   └── 添加预设图层 (图库)
├── 编辑工具 (大组)
│   ├── 编辑工具 (小组/按钮面板)
│   │   └── (预留扩展)
│   └── 动态工具 (小组/按钮面板)
│       ├── 节点距离检查
│       └── 图形重叠检查
├── 分析工具 (大组)
│   └── 计算 (小组/按钮面板)
│       └── 计算面积
├── 转换工具 (大组)
│   ├── 文本 (小组/按钮面板)
│   │   ├── 要素类转TXT
│   │   └── TXT转SHP
│   ├── 坐标 (小组/按钮面板)
│   │   └── 特殊坐标转换
│   ├── 输出 (小组/按钮面板)
│   │   ├── 下载在线影像
│   │   └── 导出布局
│   └── Overture (小组/按钮面板)
│       └── 启动Overture Maps数据加载器
├── 数据处理 (大组)
│   └── 图形处理 (小组/按钮面板)
│       └── (预留扩展)
├── 用户 (大组)
│   ├── AI助手 (独立按钮)
│   └── 用户配置 (小组/按钮面板)
│       ├── 设置
│       ├── 授权管理
│       └── 关于
└── 快速访问 (大组)
    ├── 浏览工具
    ├── 选择工具
    ├── 清除选择
    ├── 报告导航
    ├── 布局选择
    └── 测量工具
```

## 详细配置信息

### 1. 选项卡层 (最顶层)
- **ID**: `XIAOFUTools_Tab1`
- **标题**: "XIAOFU工具箱"
- **快捷键**: X

### 2. 大组层 (中间层)

#### 2.1 通用工具组
- **ID**: `XIAOFUTools_CommonGroup`
- **标题**: "通用工具"
- **包含**: 2个按钮面板 + 1个独立按钮 + 1个图库

#### 2.2 编辑工具组
- **ID**: `XIAOFUTools_EditGroup`
- **标题**: "编辑工具"
- **包含**: 1个按钮面板（暂无工具）

#### 2.3 分析工具组
- **ID**: `XIAOFUTools_AnalysisGroup`
- **标题**: "分析工具"
- **包含**: 1个按钮面板

#### 2.4 转换工具组
- **ID**: `XIAOFUTools_ConvertGroup`
- **标题**: "转换工具"
- **包含**: 4个按钮面板

#### 2.5 用户组
- **ID**: `XIAOFUTools_UserGroup`
- **标题**: "用户"
- **包含**: 1个独立按钮 + 1个按钮面板

#### 2.6 快速访问组
- **ID**: `XIAOFUTools_QuickAccessGroup`
- **标题**: "快速访问"
- **包含**: 6个系统工具引用

### 3. 小组层 (按钮面板定义 - 较低层)

#### 3.1 通用工具按钮面板1
- **ID**: `XIAOFUTools_CommonButtonPalette`
- **标题**: "通用"
- **类型**: 下拉按钮面板
- **工具数量**: 3个

#### 3.2 通用工具按钮面板2
- **ID**: `XIAOFUTools_Common2ButtonPalette`
- **标题**: "通用2"
- **类型**: 下拉按钮面板
- **工具数量**: 6个

#### 3.3 编辑工具按钮面板
- **ID**: `XIAOFUTools_EditButtonPalette`
- **标题**: "编辑工具"
- **类型**: 下拉按钮面板
- **工具数量**: 0个（预留）

#### 3.4 计算工具按钮面板
- **ID**: `XIAOFUTools_AnalysisButtonPalette`
- **标题**: "计算"
- **类型**: 下拉按钮面板
- **工具数量**: 1个

#### 3.5 文本工具按钮面板
- **ID**: `XIAOFUTools_ConvertButtonPalette`
- **标题**: "文本"
- **类型**: 下拉按钮面板
- **工具数量**: 2个
- **说明**: 隶属于"转换工具"大组，专门处理文本类转换

#### 3.6 坐标工具按钮面板
- **ID**: `XIAOFUTools_CoordinateButtonPalette`
- **标题**: "坐标"
- **类型**: 下拉按钮面板
- **工具数量**: 1个
- **说明**: 隶属于"转换工具"大组，专门处理坐标转换

#### 3.7 输出工具按钮面板
- **ID**: `XIAOFUTools_OutputButtonPalette`
- **标题**: "输出"
- **类型**: 下拉按钮面板
- **工具数量**: 2个
- **说明**: 隶属于"转换工具"大组，专门处理输出类工具

#### 3.8 Overture工具按钮面板
- **ID**: `XIAOFUTools_OvertureLoaderButton`
- **标题**: "Overture"
- **类型**: 按钮面板
- **工具数量**: 1个
- **说明**: 隶属于"转换工具"大组，专门处理Overture Maps数据

#### 3.9 用户配置按钮面板
- **ID**: `XIAOFUTools_UserConfigButtonPalette`
- **标题**: "用户配置"
- **类型**: 下拉按钮面板
- **工具数量**: 1个
- **说明**: 隶属于"用户"大组，专门处理用户配置相关功能

### 4. 具体工具层 (最底层)

#### 4.1 通用工具
1. **批量添加数据** (`XIAOFUTools_BatchAddDataButton`)
   - 功能: 扫描文件夹中的GIS数据并批量添加到地图

2. **要素顺序编号** (`XIAOFUTools_GroupNumberingButton`)
   - 功能: 按指定分组字段对要素进行顺序编号

3. **地块中文编号** (`XIAOFUTools_ChineseNumberingButton`)
   - 功能: 使用中文数字（一、二、三...）对要素进行编号

4. **按字段批量裁剪要素** (`XIAOFUTools_BatchLayerClipButton`)
   - **功能**: 根据字段值批量裁剪要素并导出为Shapefile
   - **核心特性**:
     - 支持多种字段类型
     - 可选择裁剪方式（交集、包含等）
     - 自动处理输出文件命名
   - **技术实现**: 基于ArcGIS Pro裁剪地理处理工具
   - **授权要求**: 需要"按字段批量裁剪要素工具"授权

5. **字段复制工具** (`XIAOFUTools_FieldCopyToolButton`)
   - **功能**: 在图层间复制字段值，支持基于空间关系或属性关系的字段映射
   - **核心特性**:
     - 支持多种空间关系（相交、包含、邻近等）
     - 支持属性关系匹配
     - 可选择复制单个或多个字段
     - 支持字段类型自动转换
   - **技术实现**: 使用ArcGIS Pro空间分析和属性查询API
   - **授权要求**: 需要"字段复制工具"授权

6. **根据范围批量裁剪要素图层** (`XIAOFUTools_RangeClipToolButton`)
   - **功能**: 使用指定范围（矩形、多边形或图层范围）批量裁剪多个要素图层
   - **核心特性**:
     - 支持多种范围定义方式
     - 可同时处理多个图层
     - 支持保持原始属性信息
     - 自动处理输出文件命名
   - **技术实现**: 基于ArcGIS Pro裁剪地理处理工具
   - **授权要求**: 需要"根据范围批量裁剪要素图层工具"授权

7. **生成四至坐标点** (`XIAOFUTools_BoundaryPointGeneratorButton`)
   - **功能**: 根据要素边界生成东南西北四至坐标点
   - **核心特性**:
     - 自动计算要素外包矩形
     - 生成标准四至点要素
     - 支持坐标信息标注
     - 可选择输出坐标格式
   - **技术实现**: 使用ArcGIS Pro几何计算API
   - **授权要求**: 需要"生成四至坐标点工具"授权

8. **批量定义投影** (`XIAOFUTools_BatchProjectionDefinitionButton`)
   - **功能**: 批量为图层定义坐标系，解决坐标系未定义问题
   - **核心特性**:
     - 支持常用坐标系快速选择
     - 可批量处理多个图层
     - 支持坐标系参数验证
     - 提供坐标系搜索功能
   - **技术实现**: 使用ArcGIS Pro坐标系管理API
   - **授权要求**: 需要"批量定义投影工具"授权

9. **批量修复几何** (`XIAOFUTools_BatchGeometryRepairButton`)
   - **功能**: 批量修复选中图层的几何错误（自相交、空几何等）
   - **核心特性**:
     - 自动检测几何错误类型
     - 支持多种修复策略
     - 提供修复前后对比
     - 生成修复报告
   - **技术实现**: 使用ArcGIS Pro几何验证和修复API
   - **授权要求**: 需要"批量修复几何工具"授权

#### 独立工具

10. **查看面积** (`XIAOFUTools_ViewAreaButton`)
    - **功能**: 快速查看选中要素的面积信息，支持多种面积单位显示
    - **核心特性**:
      - 实时计算选中要素面积
      - 支持多种面积单位（平方米、亩、公顷等）
      - 显示总面积和单个要素面积
      - 支持面积统计导出
    - **技术实现**: 使用ArcGIS Pro几何计算API
    - **授权要求**: 需要"查看面积工具"授权

#### 图库工具

11. **添加预设图层** (`XIAOFUTools_PresetLayersGallery`)
    - **功能**: 快速添加预设的常用图层到地图，提高工作效率
    - **核心特性**:
      - 预设常用底图和专题图层
      - 支持自定义图层模板
      - 一键添加多个相关图层
      - 自动配置图层样式
    - **技术实现**: 基于ArcGIS Pro图层管理API
    - **授权要求**: 无需特殊授权

### 编辑工具组

#### 编辑工具面板
- **状态**: 预留扩展，为未来编辑功能预留接口

#### 动态工具面板

1. **节点距离检查** (`XIAOFUTools_NodeDistanceCheckButton`)
   - **功能**: 检查要素节点间的距离，发现过近或重复的节点
   - **核心特性**:
     - 可设置最小距离阈值
     - 高亮显示问题节点
     - 生成检查报告
     - 支持批量修复
   - **技术实现**: 使用ArcGIS Pro几何分析API
   - **授权要求**: 需要"节点距离检查工具"授权

2. **图形重叠检查** (`XIAOFUTools_OverlapCheckButton`)
   - **功能**: 检查要素间的重叠情况，发现数据质量问题
   - **核心特性**:
     - 支持同图层和跨图层检查
     - 可设置重叠容差
     - 可视化显示重叠区域
     - 生成重叠统计报告
   - **技术实现**: 使用ArcGIS Pro空间分析API
   - **授权要求**: 需要"图形重叠检查工具"授权

### 分析工具组

#### 计算工具面板

1. **计算面积** (`XIAOFUTools_AreaCalculatorButton`)
   - **功能**: 计算选中要素的面积并添加到属性表，支持多种计算模式
   - **核心特性**:
     - 支持多种面积单位计算
     - 可选择目标字段或创建新字段
     - 支持投影面积和测地面积计算
     - 批量处理多个图层
   - **技术实现**: 使用ArcGIS Pro几何计算和属性编辑API
   - **授权要求**: 需要"计算面积工具"授权

### 转换工具组

#### 文本转换面板

1. **要素类转TXT** (`XIAOFUTools_FeatureToTxtButton`)
   - **功能**: 将要素类数据导出为文本文件，支持多种文本格式
   - **核心特性**:
     - 支持CSV、TXT、JSON等格式导出
     - 可选择导出字段
     - 支持坐标信息导出
     - 自定义分隔符和编码
   - **技术实现**: 使用ArcGIS Pro数据访问API和文件I/O操作
   - **授权要求**: 需要"要素类转TXT工具"授权

2. **TXT转SHP** (`XIAOFUTools_TxtToFeatureButton`)
   - **功能**: 将文本文件转换为Shapefile格式，支持点、线、面要素创建
   - **核心特性**:
     - 智能识别坐标字段
     - 支持多种文本格式解析
     - 自动推断字段类型
     - 支持坐标系定义
   - **技术实现**: 使用ArcGIS Pro要素创建API
   - **授权要求**: 需要"TXT转SHP工具"授权

#### 坐标转换面板

3. **特殊坐标转换** (`XIAOFUTools_SpecialCoordinateTransformButton`)
   - **功能**: 支持WGS84、GCJ02、BD09坐标系之间的相互转换
   - **核心特性**:
     - 支持多种国内常用坐标系
     - 高精度转换算法
     - 批量转换处理
     - 转换精度验证
   - **技术实现**: 内置坐标转换算法，基于ArcGIS Pro几何变换API
   - **授权要求**: 需要"特殊坐标转换工具"授权

#### 输出工具面板

4. **下载在线影像** (`XIAOFUTools_DownloadOnlineImageryButton`)
   - **功能**: 下载在线地图瓦片并合成为本地影像文件
   - **核心特性**:
     - 支持多种在线地图源
     - 可设置下载范围和分辨率
     - 支持多线程下载
     - 自动瓦片拼接和地理配准
   - **技术实现**: 使用HTTP客户端和图像处理库
   - **授权要求**: 需要"下载在线影像工具"授权

5. **导出布局** (`XIAOFUTools_ExportLayoutButton`)
   - **功能**: 批量导出地图布局为图片或PDF文件
   - **核心特性**:
     - 支持多种输出格式（PNG、JPG、PDF等）
     - 可设置输出分辨率和质量
     - 支持批量处理多个布局
     - 自定义输出文件命名规则
   - **技术实现**: 使用ArcGIS Pro布局导出API
   - **授权要求**: 需要"导出布局工具"授权

#### Overture工具面板

6. **启动Overture Maps数据加载器** (`XIAOFUTools_OvertureLoaderButton`)
   - **功能**: 加载和处理Overture Maps开源地图数据
   - **核心特性**:
     - 支持Overture Maps数据格式
     - 提供数据预览和筛选
     - 支持数据格式转换
     - 集成数据质量检查
   - **技术实现**: 使用DuckDB进行数据处理，集成Overture Maps API
   - **授权要求**: 需要"Overture Maps数据加载器"授权

### 数据处理组

#### 图形处理面板
- **状态**: 预留扩展，为未来图形处理功能预留接口

### 用户组

#### 独立工具

1. **AI助手** (`XIAOFUTools_AIAssistantButton`)
   - **功能**: 集成AI对话功能，提供GIS相关问题解答和操作指导
   - **核心特性**:
     - 支持多种LLM模型（OpenAI、Claude、Kimi等）
     - 智能GIS操作建议
     - 流式对话输出
     - 上下文记忆功能
     - 专业GIS知识库
   - **技术实现**: 基于WebView2的现代Web界面，集成多种AI API
   - **授权要求**: 需要"AI助手工具"授权

#### 用户配置面板

2. **设置** (`XIAOFUTools_SettingsButton`)
   - **功能**: 工具箱全局设置和配置管理
   - **核心特性**:
     - 工具授权配置
     - 界面主题设置
     - 默认路径配置
     - 性能参数调优
   - **技术实现**: 基于WPF的设置界面，使用配置文件存储
   - **授权要求**: 无需特殊授权

3. **授权管理** (`XIAOFUTools_AuthorizationButton`)
   - **功能**: 管理工具授权状态和许可证信息
   - **核心特性**:
     - 查看授权状态
     - 许可证激活和更新
     - 授权到期提醒
     - 使用统计信息
   - **技术实现**: 基于加密算法的授权验证系统
   - **授权要求**: 无需特殊授权

4. **关于** (`XIAOFUTools_AboutButton`)
   - **功能**: 显示工具箱版本信息和开发者信息
   - **核心特性**:
     - 版本号和构建信息
     - 开发者联系方式
     - 更新日志
     - 技术支持信息
   - **技术实现**: 简单的信息展示对话框
   - **授权要求**: 无需特殊授权

### 快速访问组

快速访问组集成了ArcGIS Pro的常用工具，提供便捷的操作入口：

- **浏览工具** (`esri_mapping_exploreSplitButton`): ArcGIS Pro内置的地图浏览工具集
- **选择工具** (`esri_editing_selectToolPalette`): 要素选择工具集，包括矩形选择、套索选择等
- **清除选择** (`esri_mapping_clearSelectionButton`): 清除当前选择的要素
- **报告导航** (`esri_reports_navigateContext`): 报告和导航相关工具
- **布局选择** (`esri_layouts_selectContext`): 布局视图选择工具
- **测量工具** (`esri_mapping_measureSplitButton`): 距离和面积测量工具集

3. **清除选择** (`esri_mapping_clearSelectionButton`)
   - 功能: 系统清除选择工具引用

4. **报告导航** (`esri_reports_navigateContext`)
   - 功能: 系统报告导航工具引用

5. **布局选择** (`esri_layouts_selectContext`)
   - 功能: 系统布局选择工具引用

6. **测量工具** (`esri_mapping_measureSplitButton`)
   - 功能: 系统测量工具引用

### 5. 特殊组件

#### 5.1 图库
- **添加预设图层** (`XIAOFUTools_PresetLayersGallery`)
  - 功能: 从预设图层库中选择并添加图层到地图
  - 位置: 通用工具组

#### 5.2 自定义工具
- **绘制自定义范围** (`XIAOFUTools_CustomExtentTool`)
  - 功能: 在地图上绘制矩形以定义自定义数据范围
  - 位置: 配合OvertureLoader使用

## 工具实现模式

### 1. DockPane模式（主流模式）
大多数工具采用此模式，包含以下文件：
- **Button类**: 继承自ArcGIS.Desktop.Framework.Contracts.Button
- **DockPane类**: 继承自ArcGIS.Desktop.Framework.Contracts.DockPane
- **View.xaml**: WPF用户界面定义
- **ViewModel类**: 实现业务逻辑和数据绑定

**采用此模式的工具**:
- BatchAddData, AreaCalculator, AIAssistant, BatchGeometryRepair
- BatchLayerClip, BatchProjectionDefinition, BoundaryPointGenerator
- ChineseNumbering, GroupNumbering, RangeClipTool, ViewArea
- FeatureToTxt, DownloadOnlineImagery, ExportLayout, TxtToFeature
- SpecialCoordinateTransform, Settings

### 2. 对话框模式
使用模态对话框的轻量级工具：
- **Button类**: 继承自Button
- **View.xaml**: WPF对话框界面
- **ViewModel类**: 业务逻辑实现

**采用此模式的工具**:
- FieldCopyTool

### 3. Gallery模式
用于展示和选择预设项目：
- **Gallery类**: 继承自ArcGIS.Desktop.Framework.Contracts.Gallery
- **Template.xaml**: 项目模板定义

**采用此模式的工具**:
- PresetLayers

### 4. 特殊模式
#### 4.1 Wizard模式（OvertureLoader）
- **ShowButton类**: 显示向导的按钮
- **DockPane + ViewModel**: 向导主界面
- **Services目录**: 业务逻辑服务
- **Views目录**: 多个视图组件
- **CustomExtentTool**: 自定义地图工具

## 技术架构特点

### 1. 文件组织结构
```
Tools/
├── [ToolName]/
│   ├── [ToolName]Button.cs          # 按钮入口
│   ├── [ToolName]DockPane.cs        # 停靠窗格（可选）
│   ├── [ToolName]DockPaneView.xaml  # 界面定义
│   ├── [ToolName]DockPaneView.xaml.cs # 界面代码
│   ├── [ToolName]ViewModel.cs       # 视图模型
│   └── [其他辅助文件]               # 如对话框、服务类等
```

### 2. 命名规范
- **按钮ID**: `XIAOFUTools_[ToolName]Button`
- **停靠窗格ID**: `XIAOFUTools_[ToolName]DockPane`
- **类命名空间**: `XIAOFUTools.Tools.[ToolName]`
- **图标文件**: `Images\[ToolName]_16.png` 和 `Images\[ToolName]_32.png`

### 3. 配置集中管理
所有工具的UI配置都在`Config.daml`中统一管理：
- 选项卡和分组定义
- 按钮面板配置
- 停靠窗格声明
- 右键菜单集成
- 工具提示和帮助文本

## 修改记录

### 最新更新 (2025-07-23)
- **修改内容1**: 全面更新工具结构文档，反映项目实际状态
- **新增工具**: 下载在线影像、导出布局、TXT转SHP、特殊坐标转换、Overture Maps加载器
- **新增分组**: 坐标、输出、Overture、快速访问
- **新增章节**: 工具实现模式、技术架构特点

- **修改内容2**: 重构用户组结构
- **修改位置**: Config.daml 用户组定义和按钮面板定义
- **修改详情**:
  - 在用户组中新增"用户配置"按钮面板
  - 将"设置"按钮从独立按钮改为按钮面板内的按钮
  - 新增按钮面板ID: `XIAOFUTools_UserConfigButtonPalette`
- **修改原因**: 为用户相关功能提供更好的组织结构，便于后续扩展

### 历史修改 (2025-06-22)
- **修改内容**: 将转换工具组下的按钮面板名称从"转换工具"改为"文本"
- **修改原因**: 因为要素类转TXT是文本类转换工具，使用"文本"更准确
- **修改位置**: Config.daml 第85行
- **修改前**: `caption="转换工具"`
- **修改后**: `caption="文本"`

## 统计信息

### 工具数量统计
- **总工具数**: 18个自定义工具 + 1个图库 + 1个自定义地图工具
- **通用工具组**: 10个工具（3+6+1个按钮+1个图库）
- **分析工具组**: 1个工具
- **转换工具组**: 6个工具（2+1+2+1个）
- **用户工具组**: 2个工具（1个独立按钮+1个按钮面板）
- **快速访问组**: 6个系统工具引用

### 实现模式统计
- **DockPane模式**: 16个工具
- **对话框模式**: 1个工具（FieldCopyTool）
- **Gallery模式**: 1个组件（PresetLayers）
- **特殊模式**: 1个工具（OvertureLoader）

## 结构说明

1. **层次关系**: 选项卡 → 大组 → 小组(按钮面板) → 具体工具按钮
2. **命名规范**: 大组使用功能分类名称，小组使用具体类型名称
3. **扩展性**: 每个小组都预留了添加更多工具的空间
4. **用户体验**: 通过层次化结构，用户可以快速找到所需工具
5. **技术一致性**: 大多数工具采用统一的DockPane模式，便于维护和扩展
6. **功能完整性**: 涵盖数据处理、分析计算、格式转换、输出导出等GIS常用功能
